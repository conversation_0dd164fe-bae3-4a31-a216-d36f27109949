# AMC12 2001 P5 Proof Tree

## Problem Statement
Find the product P = 1·3·5·…·9999 and show it equals 10000! ⁄ (2^5000·5000!).

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the product of all positive odd integers less than 10000 equals 10000! ⁄ (2^5000·5000!)
**Status**: [ROOT]

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use factorial decomposition approach - split 10000! into even and odd factors, then show the odd part equals our target product
**Strategy**: Factor 10000! = (product of odds) × (product of evens), then isolate the odd product
**Status**: [PROMISING]

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Express P as ∏_{k=1}^{5000} (2k - 1)
**Strategy**: Rewrite the product 1·3·5·…·9999 in summation notation
**Status**: [TO_EXPLORE]

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Decompose 10000! into odd and even factors
**Strategy**: Show 10000! = ∏_{k=1}^{5000} (2k - 1) × ∏_{k=1}^{5000} (2k)
**Status**: [TO_EXPLORE]

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Simplify the even factor product
**Strategy**: Show ∏_{k=1}^{5000} (2k) = 2^5000 × 5000!
**Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Combine results to get final formula
**Strategy**: Substitute back into 10000! = P × 2^5000 × 5000! and solve for P
**Status**: [TO_EXPLORE]

## Current Active Node
SUBGOAL_001

## Mathlib Dependencies to Explore
- Nat.factorial
- Finset.prod_range
- Nat.cast_prod
- Nat.pow_mul
- Nat.div_mul_cancel
