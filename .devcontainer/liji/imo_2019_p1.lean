import Mathlib.Data.Int.Basic
import Mathlib.Algebra.Group.Defs
import Mathlib.Logic.Function.Basic
import Mathlib.Algebra.GroupWithZero.Defs
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.Ring

-- IMO 2019 Problem 1: Find all functions f : ℤ → ℤ that satisfy f(2a) + 2f(b) = f(f(a + b)) for every pair of integers a, b.

theorem imo_2019_p1 : ∀ f : ℤ → ℤ,
  (∀ a b : ℤ, f (2 * a) + 2 * f b = f (f (a + b))) ↔
  ((∀ x, f x = 0) ∨ ∃ c : ℤ, ∀ x, f x = 2 * x + c) := by
  intro f
  constructor
  · -- Forward direction: if f satisfies the functional equation, then f is one of the two forms
    intro h
    -- Step 1: Isolate f(0) = c and derive key relations
    have eq1 : ∀ b : ℤ, f 0 + 2 * f b = f (f b) := by
      intro b
      have := h 0 b
      simp at this
      exact this
    have eq2 : ∀ a : ℤ, f (2 * a) + 2 * f 0 = f (f a) := by
      intro a
      have := h a 0
      simp at this
      exact this
    have eq3 : ∀ a : ℤ, f (2 * a) = 2 * f a - f 0 := by
      intro a
      have h1 := eq1 a
      have h2 := eq2 a
      -- From eq1: f 0 + 2 * f a = f (f a)
      -- From eq2: f (2 * a) + 2 * f 0 = f (f a)
      -- Therefore: f 0 + 2 * f a = f (2 * a) + 2 * f 0
      have : f 0 + 2 * f a = f (2 * a) + 2 * f 0 := by
        rw [h1, ← h2]
      linarith

    -- Step 2: Transform to additive form
    have eq4 : ∀ a b : ℤ, f (a + b) = f a + f b - f 0 := by
      intro a b
      have orig := h a b
      have h1 := eq1 b
      have h3 := eq3 a
      -- Original: f(2a) + 2f(b) = f(f(a + b))
      -- From eq3: f(2a) = 2f(a) - f(0)
      -- From eq1: f(0) + 2f(b) = f(f(b))
      -- So: 2f(a) - f(0) + 2f(b) = f(f(a + b))
      -- And: f(0) + 2f(a + b) = f(f(a + b)) by eq1
      have h_target := eq1 (a + b)
      rw [h3] at orig
      rw [← h_target] at orig
      linarith

    -- Step 3: Solve Cauchy equation
    let g := fun x => f x - f 0
    have cauchy : ∀ a b : ℤ, g (a + b) = g a + g b := by
      intro a b
      simp [g]
      have := eq4 a b
      linarith
    have linear : ∃ d : ℤ, g = fun x => d * x := by
      -- Use direct construction: d = g(1)
      use g 1
      funext x
      -- We'll prove this by showing g(x) = x * g(1) for all x
      rw [mul_comm]
      -- This follows from additivity of g on integers using induction
      induction x using Int.induction_on with
      | hz => -- Base case: x = 0
        simp [g]
      | hp n ih => -- Positive case: if g(n) = n * g(1), then g(n + 1) = (n + 1) * g(1)
        rw [cauchy, ih]
        rw [add_mul, one_mul]
      | hn n ih => -- Negative case: if g(-n) = (-n) * g(1), then g(-n - 1) = (-n - 1) * g(1)
        have h_rewrite : -(↑n : ℤ) - 1 = (-(↑n : ℤ)) + (-1) := by ring
        rw [h_rewrite, cauchy, ih]
        have g_neg_one : g (-1) = -g 1 := by
          have : g 0 = g (1 + (-1)) := by ring
          rw [cauchy] at this
          have g_zero : g 0 = 0 := by simp [g]
          rw [g_zero] at this
          linarith
        rw [g_neg_one]
        ring

    -- Step 4: Determine constraints on d and c
    obtain ⟨d, hd⟩ := linear
    have constraint1 : d * (d - 2) = 0 := by
      -- This follows from substituting f(x) = dx + c into the original equation
      -- From g(x) = f(x) - f(0) = dx, we have f(x) = dx + f(0)
      have f_form : ∀ x, f x = d * x + f 0 := by
        intro x
        have : f x = g x + f 0 := by simp [g]
        rw [this, hd]
        ring_nf
      -- Use the original equation with a=1, b=0
      have h1 := h 1 0
      -- Substitute f(x) = dx + f(0) everywhere
      have lhs1 : f (2 * 1) + 2 * f 0 = d * (2 * 1) + f 0 + 2 * f 0 := by
        rw [f_form]; ring_nf
      have rhs1 : f (f (1 + 0)) = f (f 1) := by simp
      have f1_val : f 1 = d * 1 + f 0 := f_form 1
      have rhs1_expanded : f (f 1) = f (d + f 0) := by rw [f1_val]; ring
      have rhs1_final : f (d + f 0) = d * (d + f 0) + f 0 := f_form (d + f 0)
      rw [lhs1, rhs1, rhs1_expanded, rhs1_final] at h1
      -- Now h1: 2d + 3f(0) = d² + df(0) + f(0)
      -- Simplifying: 2d + 2f(0) = d² + df(0)
      -- Rearranging: 2d - d² = df(0) - 2f(0) = f(0)(d - 2)
      have simplified1 : 2 * d + 2 * f 0 = d * d + d * f 0 := by
        -- From h1: 2d + 3f(0) = d² + df(0) + f(0)
        -- Subtract f(0) from both sides: 2d + 2f(0) = d² + df(0)
        have h1_rearranged : 2 * d + 2 * f 0 = d * d + d * f 0 := by linarith [h1]
        exact h1_rearranged
      -- Use the original equation with a=0, b=0
      have h2 := h 0 0
      have lhs2 : f (2 * 0) + 2 * f 0 = f 0 + 2 * f 0 := by simp
      have rhs2 : f (f (0 + 0)) = f (f 0) := by simp
      have f0_val : f 0 = d * 0 + f 0 := by rw [f_form]; simp
      have rhs2_expanded : f (f 0) = f (f 0) := rfl
      have rhs2_final : f (f 0) = d * f 0 + f 0 := by rw [f_form]
      rw [lhs2, rhs2, rhs2_final] at h2
      -- Now h2: 3f(0) = df(0) + f(0)
      -- Simplifying: 2f(0) = df(0), so f(0)(2 - d) = 0
      have simplified2 : 2 * f 0 = d * f 0 := by
        -- From h2: 3f(0) = df(0) + f(0)
        -- Subtract f(0): 2f(0) = df(0)
        have h2_rearranged : 2 * f 0 = d * f 0 := by linarith [h2]
        exact h2_rearranged
      -- From simplified1: 2d + 2f(0) = d² + 2f(0)
      -- From simplified2: 2f(0) = df(0)
      -- Substituting: 2d + df(0) = d² + df(0)
      -- Therefore: 2d = d², so d(d - 2) = 0
      have eq_2d_d2 : 2 * d = d * d := by
        -- From simplified1: 2d + 2f(0) = d² + 2f(0)
        -- Cancel 2f(0) from both sides: 2d = d²
        linarith [simplified1]
      -- Convert 2d = d² to d(d-2) = 0
      have : d * d - 2 * d = 0 := by linarith [eq_2d_d2]
      have : d * (d - 2) = 0 := by ring_nf at this ⊢; exact this
      exact this
    have constraint2 : (d - 2) * f 0 = 0 := by
      -- This follows from the original equation with a=0, b=0
      -- We get 3f(0) = df(0) + f(0), so 2f(0) = df(0), hence f(0)(2 - d) = 0
      have f_form : ∀ x, f x = d * x + f 0 := by
        intro x
        have : f x = g x + f 0 := by simp [g]
        rw [this, hd]
        ring_nf
      have h2 := h 0 0
      have lhs2 : f (2 * 0) + 2 * f 0 = f 0 + 2 * f 0 := by simp
      have rhs2 : f (f (0 + 0)) = f (f 0) := by simp
      have rhs2_final : f (f 0) = d * f 0 + f 0 := by rw [f_form]
      rw [lhs2, rhs2, rhs2_final] at h2
      -- Now h2: 3f(0) = df(0) + f(0), so 2f(0) = df(0)
      have h_eq : 2 * f 0 = d * f 0 := by linarith [h2]
      -- This gives f(0)(2 - d) = 0, so (d - 2) * f(0) = 0
      have h_factor : f 0 * (2 - d) = 0 := by linarith [h_eq]
      have h_rearrange : f 0 * (2 - d) = -(f 0 * (d - 2)) := by ring
      rw [h_rearrange] at h_factor
      have h_neg_zero : -(f 0 * (d - 2)) = 0 := h_factor
      have h_zero : f 0 * (d - 2) = 0 := by linarith [h_neg_zero]
      rw [mul_comm] at h_zero
      exact h_zero

    -- Step 5: Case analysis
    have h_cases : d = 0 ∨ d = 2 := by
      rw [mul_eq_zero] at constraint1
      cases constraint1 with
      | inl h1 => left; exact h1
      | inr h2 => right; sorry
    cases h_cases with
    | inl h_d_zero =>
      -- Case d = 0
      left
      -- If d = 0, then g(x) = 0 for all x, so f(x) = f(0) for all x
      -- From constraint2: (0 - 2) * f(0) = 0, so -2 * f(0) = 0, hence f(0) = 0
      -- Therefore f(x) = 0 for all x
      intro x
      have h_g_zero : g x = 0 := by
        rw [hd, h_d_zero]
        simp
      simp [g] at h_g_zero
      have h_f0_zero : f 0 = 0 := by
        rw [h_d_zero] at constraint2
        simp at constraint2
        exact constraint2
      rw [h_f0_zero] at h_g_zero
      simp at h_g_zero
      exact h_g_zero
    | inr h_d_two =>
      -- Case d = 2
      right
      use f 0
      -- If d = 2, then g(x) = 2x, so f(x) = g(x) + f(0) = 2x + f(0)
      intro x
      have h_g_form : g x = 2 * x := by
        rw [hd, h_d_two]
      simp [g] at h_g_form
      linarith

  · -- Backward direction: verify solutions satisfy the equation
    intro h
    cases' h with h1 h2
    · -- Case f(x) = 0
      intro a b
      rw [h1, h1, h1]
      simp
    · -- Case f(x) = 2x + c
      obtain ⟨c, hc⟩ := h2
      intro a b
      rw [hc, hc, hc]
      -- LHS: 2(2a) + c + 2(2b + c) = 4a + c + 4b + 2c = 4a + 4b + 3c
      -- RHS: 2(2(a + b) + c) + c = 4(a + b) + 2c + c = 4a + 4b + 3c
      sorry
