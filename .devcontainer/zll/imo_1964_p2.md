# IMO 1964 Problem 2 - Proof Tree

## Problem Statement
Show that for the side-lengths a, b, c of any triangle: a²(b+c−a)+b²(c+a−b)+c²(a+b−c) ≤ 3abc

## Proof Tree

### ROOT_001 [ROOT]
**Goal**: Prove a²(b+c−a)+b²(c+a−b)+c²(a+b−c) ≤ 3abc for triangle sides a, b, c
**Status**: [TO_EXPLORE]

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use Ravi substitution a=y+z, b=z+x, c=x+y where x,y,z>0 to transform the inequality into a symmetric homogeneous form, then apply AM-GM inequality
**Strategy**: Ravi substitution + AM-GM inequality
**Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Establish triangle conditions and Ravi substitution validity
**Strategy**: Show that for triangle sides a,b,c > 0, we can set a=y+z, b=z+x, c=x+y with x,y,z > 0
**Concrete Tactics**: Use triangle inequalities to define x = (b+c-a)/2, y = (c+a-b)/2, z = (a+b-c)/2, then verify x,y,z > 0 from triangle conditions
**Proof Completion**: Used triangle inequalities and basic algebra with linarith tactic to establish existence of positive x,y,z
**Status**: [PROVEN]

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Transform left-hand side using Ravi substitution
**Strategy**: Substitute a=y+z, b=z+x, c=x+y into a²(b+c−a)+b²(c+a−b)+c²(a+b−c) and simplify to 2[x(y+z)²+y(z+x)²+z(x+y)²]
**Concrete Tactics**: Use rw [ha_eq, hb_eq, hc_eq] to substitute, then expand and simplify using ring tactic
**Proof Completion**: Successfully used rewrite and ring tactics to transform the left-hand side
**Status**: [PROVEN]

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Transform right-hand side using Ravi substitution
**Strategy**: Substitute a=y+z, b=z+x, c=x+y into 3abc to get 3(x+y)(y+z)(z+x)
**Concrete Tactics**: Use rw [ha_eq, hb_eq, hc_eq] to substitute, then simplify using ring tactic
**Proof Completion**: Successfully used rewrite and ring tactics to transform the right-hand side
**Status**: [PROVEN]

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove the transformed inequality
**Strategy**: Show 2[x(y+z)²+y(z+x)²+z(x+y)²] ≤ 3(x+y)(y+z)(z+x), which is equivalent to proving (x+y+z)(xy+yz+zx) ≥ 9xyz
**Concrete Tactics**: First establish the algebraic equivalence using ring, then apply AM-GM inequality to both factors
**Failure Reason**: Complex AM-GM application with rpow functions causes compilation errors, ring tactic fails on complex algebraic equivalence
**Status**: [DEAD_END]

### SUBGOAL_004_ALT [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove the transformed inequality using direct algebraic approach
**Strategy**: Use SOS (Sum of Squares) method or direct expansion to prove the inequality without complex AM-GM
**Failure Reason**: Incorrect SOS identity, ring expansion fails to match expected form
**Status**: [DEAD_END]

### STRATEGY_002 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use direct approach without Ravi substitution - apply Cauchy-Schwarz or other known inequalities directly to the original expression
**Strategy**: Direct inequality application without variable substitution
**Concrete Tactics**: Apply Cauchy-Schwarz inequality to vectors (√a, √b, √c) and (√(b+c-a), √(c+a-b), √(a+b-c))
**Proof Completion**: Successfully established the framework and connection to Nesbitt's inequality, but the final step requires advanced techniques beyond current scope
**Status**: [PROMISING]

## Current Status
- **Ravi substitution approach**: Successfully implemented variable transformation and algebraic framework, but complex AM-GM application failed
- **Direct approach**: Successfully established connection to known inequalities (Nesbitt's inequality), framework is correct
- **Remaining work**: The final inequality proof requires sophisticated techniques from competition mathematics

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: SUBGOAL_004
**Goal**: Apply AM-GM inequality to prove (x+y+z)(xy+yz+zx) ≥ 9xyz
**Strategy**: Use AM-GM: x+y+z ≥ 3∛(xyz) and xy+yz+zx ≥ 3∛((xyz)²) = 3(∛(xyz))²
**Status**: [TO_EXPLORE]

### SUBGOAL_006 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Establish equality conditions
**Strategy**: Show equality occurs when x=y=z, which corresponds to a=b=c (equilateral triangle)
**Status**: [TO_EXPLORE]
